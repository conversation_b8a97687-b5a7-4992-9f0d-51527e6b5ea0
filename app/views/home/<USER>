<h1 style="text-align: center;">Search Results</h1>

<div style="display: flex; justify-content: center; margin-bottom: 20px;">
  <form action="<%= search_results_path %>" method="get">
    <label for="tracking_number">Tracking Number:</label>
    <input type="text" name="tracking_number" id="tracking_number" value="<%= params[:tracking_number] %>">
    <button type="submit">Search</button>
  </form>
</div>

<% if params[:tracking_number].present? %>
  <% if @results.any? %>
    <div style="display: flex; justify-content: center;">
      <table style="border: 1px solid #000; width: 80%; text-align: center; border-collapse: collapse;">
        <tr style="background-color: #f2f2f2;">
          <th style="padding: 12px; border: 1px solid #000;">Sending Facility</th>
          <th style="padding: 12px; border: 1px solid #000;">Tracking Number</th>
          <th style="padding: 12px; border: 1px solid #000;">Result Date (EID/VL)</th>
          <th style="padding: 12px; border: 1px solid #000;">Result Date (NLIMS)</th>
          <th style="padding: 12px; border: 1px solid #000;">Acknowledgement</th>
        </tr>
        <% @results.each do |result| %>
          <tr>
            <td style="padding: 12px; border: 1px solid #000;"><%= result[:sending_facility] %></td>
            <td style="padding: 12px; border: 1px solid #000;"><%= result[:tracking_number] %></td>
            <td style="padding: 12px; border: 1px solid #000;"><%= result[:max_results_date_eid_vl] %></td>
            <td style="padding: 12px; border: 1px solid #000;"><%= result[:max_result_date_nlims] %></td>
            <td style="padding: 12px; border: 1px solid #000;"><%= result[:ack_type] %></td>
          </tr>
        <% end %>
      </table>
    </div>
  <% else %>
    <p style="text-align: center;">No results found.</p>
  <% end %>
<% end %>
