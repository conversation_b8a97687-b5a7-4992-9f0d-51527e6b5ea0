<h1 style="text-align: center;">Integrated Sites</h1>

<!-- Integration Status Last Update Info -->
<div style="text-align: center; margin-bottom: 20px; padding: 10px; background-color: #e8f4fd; border: 1px solid #bee5eb; border-radius: 5px;">
  <strong>App Status & Ping Status Last Updated:</strong> <%= @sites[:integration_status_last_update] %>
</div>

<!-- Summary Cards -->
<div style="display: flex; justify-content: center; gap: 15px; margin-bottom: 25px; flex-wrap: wrap;">
  <div style="background: white; padding: 15px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); text-align: center; min-width: 120px;">
    <div style="font-size: 24px; font-weight: bold; color: #007bff;" id="total-count"><%= @sites[:data].size %></div>
    <div style="font-size: 12px; color: #6c757d;">Total Sites</div>
  </div>
  <div style="background: white; padding: 15px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); text-align: center; min-width: 120px;">
    <div style="font-size: 24px; font-weight: bold; color: #dc3545;" id="sync-issues">0</div>
    <div style="font-size: 12px; color: #6c757d;">Out of Sync</div>
  </div>
  <div style="background: white; padding: 15px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); text-align: center; min-width: 120px;">
    <div style="font-size: 24px; font-weight: bold; color: #dc3545;" id="app-issues">0</div>
    <div style="font-size: 12px; color: #6c757d;">App Down</div>
  </div>
  <div style="background: white; padding: 15px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); text-align: center; min-width: 120px;">
    <div style="font-size: 24px; font-weight: bold; color: #dc3545;" id="ping-issues">0</div>
    <div style="font-size: 12px; color: #6c757d;">Ping Failed</div>
  </div>
</div>

<!-- Search and Filters -->
<div style="display: flex; justify-content: center; margin-bottom: 20px;">
  <div style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); min-width: 600px;">
    <!-- Search -->
    <div style="margin-bottom: 15px;">
      <label for="search-input" style="display: block; margin-bottom: 5px; font-weight: 500;">Search:</label>
      <input type="text" id="search-input" placeholder="Search by facility or district" onkeyup="filterTable()"
             style="width: 100%; padding: 8px 12px; border: 1px solid #ced4da; border-radius: 4px;">
    </div>

    <!-- Filter Buttons -->
    <div>
      <label style="display: block; margin-bottom: 8px; font-weight: 500;">Quick Filters:</label>
      <div style="display: flex; gap: 10px; flex-wrap: wrap;">
        <button onclick="showProblems()" style="padding: 8px 16px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer;">
          Show Problems Only
        </button>
        <button onclick="showAll()" style="padding: 8px 16px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer;">
          Show All Sites
        </button>
      </div>
    </div>
  </div>
</div>

<div style="display: flex; justify-content: center;">
  <table id="orders-table" style="border: 1px solid #000; width: 95%; border-collapse: collapse;">
    <thead>
      <tr style="background-color: #f2f2f2;">
        <th style="padding: 8px; border: 1px solid #000; font-size: 12px; text-align: center;">#</th>
        <th style="padding: 8px; border: 1px solid #000; font-size: 12px; text-align: left;">Sending Facility</th>
        <th style="padding: 8px; border: 1px solid #000; font-size: 12px; text-align: left;">District</th>
        <th style="padding: 8px; border: 1px solid #000; font-size: 12px; text-align: left;">IP Address</th>
        <th style="padding: 8px; border: 1px solid #000; font-size: 12px; text-align: center;">Port</th>
        <th style="padding: 8px; border: 1px solid #000; font-size: 12px; text-align: center;">App Status</th>
        <th style="padding: 8px; border: 1px solid #000; font-size: 12px; text-align: center;">Ping Status</th>
        <th style="padding: 8px; border: 1px solid #000; font-size: 12px; text-align: left;">Last Synced</th>
      </tr>
    </thead>
    <tbody>
      <% @sites[:data].each_with_index do |site, index| %>
        <tr>
          <td style="padding: 8px; border: 1px solid #000; font-size: 11px; text-align: center;"><%= index + 1 %></td>
          <td style="padding: 8px; border: 1px solid #000; font-size: 11px; text-align: left;"><%= site[:sending_facility] %></td>
          <td style="padding: 8px; border: 1px solid #000; font-size: 11px; text-align: left;"><%= site[:district] || 'N/A' %></td>
          <td style="padding: 8px; border: 1px solid #000; font-size: 11px; text-align: left;"><%= site[:ip_address] || 'N/A' %></td>
          <td style="padding: 8px; border: 1px solid #000; font-size: 11px; text-align: center;"><%= site[:port] || 'N/A' %></td>
          <td style="padding: 8px; border: 1px solid #000; font-size: 11px; text-align: center; background-color: <%= site[:app_status] == 'Running' ? '#d4edda' : '#f8d7da' %>; color: <%= site[:app_status] == 'Running' ? '#155724' : '#721c24' %>;">
            <strong><%= site[:app_status] %></strong>
          </td>
          <td style="padding: 8px; border: 1px solid #000; font-size: 11px; text-align: center; background-color: <%= site[:ping_status] == 'Success' ? '#d4edda' : '#f8d7da' %>; color: <%= site[:ping_status] == 'Success' ? '#155724' : '#721c24' %>;">
            <strong><%= site[:ping_status] %></strong>
          </td>
          <td style="padding: 8px; border: 1px solid #000; font-size: 11px; text-align: left; background-color: <%= site[:is_gt_24hr] ? '#f8d7da' : '#d4edda' %>; color: <%= site[:is_gt_24hr] ? '#721c24' : '#155724' %>;">
            <%= site[:last_sync] %>
          </td>
        </tr>
      <% end %>
    </tbody>
  </table>
</div>

<p id="no-results-message" style="text-align: center; display: none;">No orders found.</p>

<script>
  // Calculate summary when page loads
  window.addEventListener('load', function() {
    calculateSummary();
  });

  // Search function
  function filterTable() {
    const input = document.getElementById('search-input').value.toLowerCase();
    const table = document.getElementById('orders-table');
    const rows = table.getElementsByTagName('tbody')[0].getElementsByTagName('tr');
    let visibleCount = 0;

    for (const row of rows) {
      const facility = row.cells[1].innerText.toLowerCase();
      const district = row.cells[2].innerText.toLowerCase();

      if (facility.includes(input) || district.includes(input)) {
        row.style.display = '';
        visibleCount++;
      } else {
        row.style.display = 'none';
      }
    }

    // Update total count to show filtered results
    document.getElementById('total-count').innerText = visibleCount;
  }

  // Show only sites with problems
  function showProblems() {
    const table = document.getElementById('orders-table');
    const rows = table.getElementsByTagName('tbody')[0].getElementsByTagName('tr');
    let visibleCount = 0;

    for (const row of rows) {
      const appStatus = row.cells[5].innerText.trim();
      const pingStatus = row.cells[6].innerText.trim();
      const syncCell = row.cells[7];

      // Check if site has any problems
      const hasAppProblem = appStatus === 'Down';
      const hasPingProblem = pingStatus === 'Failed';
      const hasSyncProblem = syncCell.style.backgroundColor.includes('f8d7da') ||
                            syncCell.style.backgroundColor.includes('#f8d7da');

      if (hasAppProblem || hasPingProblem || hasSyncProblem) {
        row.style.display = '';
        visibleCount++;
      } else {
        row.style.display = 'none';
      }
    }

    document.getElementById('total-count').innerText = visibleCount;
    document.getElementById('search-input').value = '';
  }

  // Show all sites
  function showAll() {
    const table = document.getElementById('orders-table');
    const rows = table.getElementsByTagName('tbody')[0].getElementsByTagName('tr');

    for (const row of rows) {
      row.style.display = '';
    }

    document.getElementById('total-count').innerText = rows.length;
    document.getElementById('search-input').value = '';
    calculateSummary();
  }

  // Calculate summary statistics
  function calculateSummary() {
    const table = document.getElementById('orders-table');
    const rows = table.getElementsByTagName('tbody')[0].getElementsByTagName('tr');

    let syncIssues = 0;
    let appIssues = 0;
    let pingIssues = 0;

    for (const row of rows) {
      // Count app issues
      if (row.cells[5].innerText.trim() === 'Down') {
        appIssues++;
      }

      // Count ping issues
      if (row.cells[6].innerText.trim() === 'Failed') {
        pingIssues++;
      }

      // Count sync issues (red background)
      const syncCell = row.cells[7];
      if (syncCell.style.backgroundColor.includes('f8d7da') ||
          syncCell.style.backgroundColor.includes('#f8d7da')) {
        syncIssues++;
      }
    }

    // Update summary cards
    document.getElementById('sync-issues').innerText = syncIssues;
    document.getElementById('app-issues').innerText = appIssues;
    document.getElementById('ping-issues').innerText = pingIssues;
  }
</script>
