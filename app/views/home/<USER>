<h1 style="text-align: center;">Integrated Sites</h1>

<div style="display: flex; justify-content: center; margin-bottom: 20px;">
  <label for="search-input">Search by Sending Facility:</label>
  <input type="text" id="search-input" placeholder="Enter sending facility" onkeyup="filterTable()">
</div>

<div style="display: flex; justify-content: center;">
  <table id="orders-table" style="border: 1px solid #000; width: 80%; text-align: center; border-collapse: collapse;">
    <thead>
      <tr style="background-color: #f2f2f2;">
        <th style="padding: 12px; border: 1px solid #000;">#</th>
        <th style="padding: 12px; border: 1px solid #000;">Sending Facility</th>
        <th style="padding: 12px; border: 1px solid #000;">Last Synced</th>
      </tr>
    </thead>
    <tbody>
      <% @sites.each_with_index do |order, index| %>
        <tr>
          <td style="padding: 12px; border: 1px solid #000;"><%= index + 1 %></td>
          <td style="padding: 12px; border: 1px solid #000;"><%= order[:sending_facility] %></td>
          <td style="padding: 12px; border: 1px solid #000; background-color: <%= order[:is_gt_24hr] ? 'red' : '#f2f2f2;' %>;"><%= order[:last_sync] %></td>
        </tr>
      <% end %>
    </tbody>
  </table>
</div>

<p id="no-results-message" style="text-align: center; display: none;">No orders found.</p>

<script>
  function filterTable() {
    const input = document.getElementById('search-input').value.toLowerCase();
    const table = document.getElementById('orders-table');
    const rows = table.getElementsByTagName('tbody')[0].getElementsByTagName('tr');
    let hasResults = false;

    for (const row of rows) {
      const sendingFacility = row.cells[1].innerText.toLowerCase();
      if (sendingFacility.includes(input)) {
        row.style.display = '';
        hasResults = true;
      } else {
        row.style.display = 'none';
      }
    }

    // Show/hide "No orders found" message
    document.getElementById('no-results-message').style.display = hasResults ? 'none' : 'block';
  }
</script>
