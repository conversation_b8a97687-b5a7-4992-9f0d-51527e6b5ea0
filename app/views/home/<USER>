<h1 style="text-align: center;">Integrated Sites</h1>

<!-- Integration Status Last Update Info -->
<div style="text-align: center; margin-bottom: 20px; padding: 10px; background-color: #e8f4fd; border: 1px solid #bee5eb; border-radius: 5px;">
  <strong>App Status & Ping Status Last Updated:</strong> <%= @sites[:integration_status_last_update] %>
</div>

<!-- Summary Section -->
<div style="display: flex; justify-content: center; margin-bottom: 25px;">
  <div style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); border: 1px solid #dee2e6; min-width: 800px;">
    <h3 style="margin: 0 0 15px 0; color: #2c3e50; text-align: center;">Sites Summary</h3>
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; text-align: center;">
      <div style="padding: 10px; background: #f8f9fa; border-radius: 5px;">
        <div style="font-size: 24px; font-weight: bold; color: #007bff;" id="total-sites"><%= @sites[:data].size %></div>
        <div style="font-size: 12px; color: #6c757d;">Total Sites</div>
      </div>
      <div style="padding: 10px; background: #f8d7da; border-radius: 5px;">
        <div style="font-size: 24px; font-weight: bold; color: #721c24;" id="out-of-sync-count">0</div>
        <div style="font-size: 12px; color: #721c24;">Out of Sync (>48hrs)</div>
      </div>
      <div style="padding: 10px; background: #f8d7da; border-radius: 5px;">
        <div style="font-size: 24px; font-weight: bold; color: #721c24;" id="app-down-count">0</div>
        <div style="font-size: 12px; color: #721c24;">App Down</div>
      </div>
      <div style="padding: 10px; background: #f8d7da; border-radius: 5px;">
        <div style="font-size: 24px; font-weight: bold; color: #721c24;" id="ping-failed-count">0</div>
        <div style="font-size: 12px; color: #721c24;">Ping Failed</div>
      </div>
    </div>
  </div>
</div>

<!-- Search and Filter Section -->
<div style="display: flex; justify-content: center; margin-bottom: 20px;">
  <div style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); border: 1px solid #dee2e6; min-width: 800px;">
    <!-- Search Input -->
    <div style="margin-bottom: 15px;">
      <label for="search-input" style="display: block; margin-bottom: 8px; font-weight: 500; color: #495057; font-size: 14px;">Search by Facility or District:</label>
      <input type="text" id="search-input" placeholder="Enter facility or district name" onkeyup="filterTable()"
             style="width: 100%; padding: 10px 12px; border: 1px solid #ced4da; border-radius: 4px; font-size: 14px; box-sizing: border-box;">
    </div>

    <!-- Filter Buttons -->
    <div>
      <label style="display: block; margin-bottom: 8px; font-weight: 500; color: #495057; font-size: 14px;">Quick Filters:</label>
      <div style="display: flex; gap: 10px; flex-wrap: wrap;">
        <button onclick="filterByStatus('out-of-sync')" id="filter-sync"
                style="padding: 10px 16px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 13px; font-weight: 500; transition: background-color 0.2s;">
          Out of Sync
        </button>
        <button onclick="filterByStatus('app-down')" id="filter-app"
                style="padding: 10px 16px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 13px; font-weight: 500; transition: background-color 0.2s;">
          App Down
        </button>
        <button onclick="filterByStatus('ping-failed')" id="filter-ping"
                style="padding: 10px 16px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 13px; font-weight: 500; transition: background-color 0.2s;">
          Ping Failed
        </button>
        <button onclick="clearFilters()" id="clear-filters"
                style="padding: 10px 16px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 13px; font-weight: 500; transition: background-color 0.2s;">
          Clear All
        </button>
      </div>
    </div>
  </div>
</div>

<div style="display: flex; justify-content: center;">
  <table id="orders-table" style="border: 1px solid #000; width: 95%; border-collapse: collapse;">
    <thead>
      <tr style="background-color: #f2f2f2;">
        <th style="padding: 8px; border: 1px solid #000; font-size: 12px; text-align: center;">#</th>
        <th style="padding: 8px; border: 1px solid #000; font-size: 12px; text-align: left;">Sending Facility</th>
        <th style="padding: 8px; border: 1px solid #000; font-size: 12px; text-align: left;">District</th>
        <th style="padding: 8px; border: 1px solid #000; font-size: 12px; text-align: left;">IP Address</th>
        <th style="padding: 8px; border: 1px solid #000; font-size: 12px; text-align: center;">Port</th>
        <th style="padding: 8px; border: 1px solid #000; font-size: 12px; text-align: left;">Last Synced Order Timestamp (CHSU)</th>
        <th style="padding: 8px; border: 1px solid #000; font-size: 12px; text-align: center;">App Status</th>
        <th style="padding: 8px; border: 1px solid #000; font-size: 12px; text-align: center;">Ping Status</th>
      </tr>
    </thead>
    <tbody>
      <% @sites[:data].each_with_index do |site, index| %>
        <tr>
          <td style="padding: 8px; border: 1px solid #000; font-size: 11px; text-align: center;"><%= index + 1 %></td>
          <td style="padding: 8px; border: 1px solid #000; font-size: 11px; text-align: left;"><%= site[:sending_facility] %></td>
          <td style="padding: 8px; border: 1px solid #000; font-size: 11px; text-align: left;"><%= site[:district] || 'N/A' %></td>
          <td style="padding: 8px; border: 1px solid #000; font-size: 11px; text-align: left;"><%= site[:ip_address] || 'N/A' %></td>
          <td style="padding: 8px; border: 1px solid #000; font-size: 11px; text-align: center;"><%= site[:port] || 'N/A' %></td>
          <td style="padding: 8px; border: 1px solid #000; font-size: 11px; text-align: left; background-color: <%= site[:is_gt_24hr] ? '#f8d7da' : '#d4edda' %>; color: <%= site[:is_gt_24hr] ? '#721c24' : '#155724' %>;">
            <%= site[:last_sync] %>
          </td>
          <td style="padding: 8px; border: 1px solid #000; font-size: 11px; text-align: center; background-color: <%= site[:app_status] == 'Running' ? '#d4edda' : '#f8d7da' %>; color: <%= site[:app_status] == 'Running' ? '#155724' : '#721c24' %>;">
            <strong><%= site[:app_status] %></strong>
          </td>
          <td style="padding: 8px; border: 1px solid #000; font-size: 11px; text-align: center; background-color: <%= site[:ping_status] == 'Success' ? '#d4edda' : '#f8d7da' %>; color: <%= site[:ping_status] == 'Success' ? '#155724' : '#721c24' %>;">
            <strong><%= site[:ping_status] %></strong>
          </td>
        </tr>
      <% end %>
    </tbody>
  </table>
</div>

<p id="no-results-message" style="text-align: center; display: none;">No orders found.</p>

<script>
  // Current active filter
  let activeFilter = null;

  // Calculate summary statistics on page load
  document.addEventListener('DOMContentLoaded', function() {
    updateSummaryStats();
  });

  // Main filter function
  function filterTable() {
    const input = document.getElementById('search-input').value.toLowerCase();
    const table = document.getElementById('orders-table');
    const rows = table.getElementsByTagName('tbody')[0].getElementsByTagName('tr');
    let hasResults = false;
    let visibleCount = 0;

    for (const row of rows) {
      const sendingFacility = row.cells[1].innerText.toLowerCase(); // Sending Facility is column 1
      const district = row.cells[2].innerText.toLowerCase(); // District is column 2

      // Check if row matches search text
      const matchesSearch = input === '' || sendingFacility.includes(input) || district.includes(input);

      // Check if row matches active filter
      let matchesFilter = true;
      if (activeFilter === 'out-of-sync') {
        // Check if the Last Synced column has red background (out of sync)
        const syncCell = row.cells[7]; // Last Synced Order Timestamp column
        const syncCellStyle = window.getComputedStyle(syncCell);
        const syncBgColor = syncCellStyle.backgroundColor;
        matchesFilter = syncBgColor.includes('248, 215, 218') || syncCell.style.backgroundColor.includes('#f8d7da') || syncCell.style.backgroundColor.includes('f8d7da');
      } else if (activeFilter === 'app-down') {
        // Check if App Status is "Down"
        const appStatusCell = row.cells[5]; // App Status column
        matchesFilter = appStatusCell.innerText.trim() === 'Down';
      } else if (activeFilter === 'ping-failed') {
        // Check if Ping Status is "Failed"
        const pingStatusCell = row.cells[6]; // Ping Status column
        matchesFilter = pingStatusCell.innerText.trim() === 'Failed';
      }

      // Show row if it matches both search and filter criteria
      if (matchesSearch && matchesFilter) {
        row.style.display = '';
        hasResults = true;
        visibleCount++;
      } else {
        row.style.display = 'none';
      }
    }

    // Show/hide "No results found" message
    document.getElementById('no-results-message').style.display = hasResults ? 'none' : 'block';

    // Update the visible count
    const totalSites = document.getElementById('total-sites');
    totalSites.innerHTML = `${visibleCount} / ${rows.length}`;
  }

  // Filter by specific status
  function filterByStatus(status) {
    // Reset all filter buttons
    document.getElementById('filter-sync').style.background = '#dc3545';
    document.getElementById('filter-app').style.background = '#dc3545';
    document.getElementById('filter-ping').style.background = '#dc3545';

    // If clicking the same filter, toggle it off
    if (activeFilter === status) {
      activeFilter = null;
      document.getElementById('search-input').value = '';
    } else {
      // Set the new active filter
      activeFilter = status;

      // Highlight the active filter button
      const buttonId = status === 'out-of-sync' ? 'filter-sync' :
                      status === 'app-down' ? 'filter-app' : 'filter-ping';
      document.getElementById(buttonId).style.background = '#28a745';
    }

    // Apply the filter
    filterTable();
  }

  // Clear all filters
  function clearFilters() {
    activeFilter = null;
    document.getElementById('search-input').value = '';
    document.getElementById('filter-sync').style.background = '#dc3545';
    document.getElementById('filter-app').style.background = '#dc3545';
    document.getElementById('filter-ping').style.background = '#dc3545';
    filterTable();
    updateSummaryStats();
  }

  // Update summary statistics
  function updateSummaryStats() {
    const table = document.getElementById('orders-table');
    const rows = table.getElementsByTagName('tbody')[0].getElementsByTagName('tr');

    let outOfSyncCount = 0;
    let appDownCount = 0;
    let pingFailedCount = 0;

    for (const row of rows) {
      // Check if out of sync (>48hrs) - look for red background color
      const syncCell = row.cells[7]; // Last Synced Order Timestamp column
      const syncCellStyle = window.getComputedStyle(syncCell);
      const syncBgColor = syncCellStyle.backgroundColor;
      // Check if background is red (rgb values for #f8d7da)
      if (syncBgColor.includes('248, 215, 218') || syncCell.style.backgroundColor.includes('#f8d7da') || syncCell.style.backgroundColor.includes('f8d7da')) {
        outOfSyncCount++;
      }

      // Check if App Status is "Down"
      const appStatusCell = row.cells[5]; // App Status column
      if (appStatusCell.innerText.trim() === 'Down') {
        appDownCount++;
      }

      // Check if Ping Status is "Failed"
      const pingStatusCell = row.cells[6]; // Ping Status column
      if (pingStatusCell.innerText.trim() === 'Failed') {
        pingFailedCount++;
      }
    }

    // Update summary counts
    document.getElementById('out-of-sync-count').innerText = outOfSyncCount;
    document.getElementById('app-down-count').innerText = appDownCount;
    document.getElementById('ping-failed-count').innerText = pingFailedCount;

    // Update total sites count
    document.getElementById('total-sites').innerText = rows.length;
  }
</script>
