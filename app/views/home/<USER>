<h1 style="text-align: center;">Orders Per Sending Facility</h1>

<div style="display: flex; justify-content: center; margin-bottom: 20px;">
  <form action="<%= sites_by_orders_path %>" method="get">
    <label for="from_date">From Date:</label>
    <input type="date" name="from_date" id="from_date" value="<%= params[:from_date] || Date.today %>">

    <label for="to_date" style="margin-left: 10px;">To Date:</label>
    <input type="date" name="to_date" id="to_date" value="<%= params[:to_date] || Date.today %>">

    <label for="sending_facility" style="margin-left: 10px;">Sending Facility:</label>
    <select name="sending_facility" id="sending_facility">
      <option value="">-- Select a Facility --</option>
      <% @sites.each do |site| %>
        <option value="<%= site %>" <%= 'selected' if site == params[:sending_facility] %>><%= site %></option>
      <% end %>
    </select>

    <button type="submit" style="margin-left: 10px;">Search</button>
  </form>
</div>

<% if @orders_data[:data].any? %>
<div style="display: flex; justify-content: center; margin-bottom: 20px;">
  <label for="search-input">Search by Tracking Number:</label>
  <input type="text" id="search-input" placeholder="Enter tracking number" onkeyup="filterTable()">
</div>
  <div style="display: flex; justify-content: center;">
    <table id="orders-table" style="border: 1px solid #000; width: 80%; text-align: center; border-collapse: collapse;">
      <thead>
      <tr style="background-color: #f2f2f2;">
        <th style="padding: 12px; border: 1px solid #000;">#</th>
        <th style="padding: 12px; border: 1px solid #000;">Tracking Number</th>
        <th style="padding: 12px; border: 1px solid #000;">Sending Facility</th>
        <th style="padding: 12px; border: 1px solid #000;">Date Created in EMR</th>
        <th style="padding: 12px; border: 1px solid #000;">Date Created in NLIMS</th>
      </tr>
      </thead>
      <tbody>
      <% @orders_data[:data].each_with_index do |order, index| %>
        <tr>
          <td style="padding: 12px; border: 1px solid #000;"><%= index + 1 %></td>
          <td style="padding: 12px; border: 1px solid #000;"><%= order.tracking_number %></td>
          <td style="padding: 12px; border: 1px solid #000;"><%= order.sending_facility %></td>
          <td style="padding: 12px; border: 1px solid #000;"><%= order.date_created %></td>
          <td style="padding: 12px; border: 1px solid #000;"><%= order.created_at %></td>
        </tr>
      <% end %>
      </tbody>
    </table>
  </div>
<% else %>
  <p style="text-align: center;">No data available for the selected date range and facility.</p>
<% end %>

<script>
  function filterTable() {
    const input = document.getElementById('search-input').value.toLowerCase();
    const table = document.getElementById('orders-table');
    const rows = table.getElementsByTagName('tbody')[0].getElementsByTagName('tr');
    let hasResults = false;

    for (const row of rows) {
      const sendingFacility = row.cells[1].innerText.toLowerCase();
      if (sendingFacility.includes(input)) {
        row.style.display = '';
        hasResults = true;
      } else {
        row.style.display = 'none';
      }
    }

    // Show/hide "No orders found" message
    document.getElementById('no-results-message').style.display = hasResults ? 'none' : 'block';
  }
</script>
