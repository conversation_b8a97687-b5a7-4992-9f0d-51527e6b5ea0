# frozen_string_literal: true

namespace :specimens do
  desc 'Load tracking numbers from specimens.json file into TrackingNumberLogger table'
  task load_data: :environment do
    require 'json'

    # Path to the specimens.json file
    file_path = File.join(Rails.root, 'specimens.json')

    unless File.exist?(file_path)
      puts "Error: specimens.json file not found at #{file_path}"
      exit 1
    end

    puts "Loading specimens data from #{file_path}..."

    # Read the file and parse JSON
    begin
      specimens_data = JSON.parse(File.read(file_path))
      total_specimens = specimens_data.size
      puts "Found #{total_specimens} specimens in the file"

      # Get the highest existing chsu_tracking_number_order_id to avoid duplicates
      last_logged_id = TrackingNumberLogger.maximum(:chsu_tracking_number_order_id) || 0
      puts "Last logged ID in database: #{last_logged_id}"

      # Filter specimens that are not already in the database
      new_specimens = specimens_data.select { |specimen| specimen['id'].to_i > last_logged_id }
      puts "Found #{new_specimens.size} new specimens to import"

      # Process in batches to avoid memory issues
      batch_size = 1000
      total_imported = 0

      new_specimens.each_slice(batch_size) do |batch|
        # Create an array of TrackingNumberLogger objects
        tracking_loggers = batch.map do |specimen|
          {
            tracking_number: specimen['tracking_number'],
            chsu_tracking_number_order_id: specimen['id']
          }
        end

        # Import the batch
        result = TrackingNumberLogger.insert_all(tracking_loggers)
        total_imported += result.rows.length

        puts "Imported batch of #{result.rows.length} specimens. Total progress: #{total_imported}/#{new_specimens.size}"
      end

      puts "Import completed. Total specimens imported: #{total_imported}"
    rescue JSON::ParserError => e
      puts "Error parsing JSON file: #{e.message}"
      exit 1
    rescue StandardError => e
      puts "Error during import: #{e.message}"
      puts e.backtrace
      exit 1
    end
  end

  desc 'Check specimens.json file structure'
  task check_file: :environment do
    require 'json'

    # Path to the specimens.json file
    file_path = File.join(Rails.root, 'specimens.json')

    unless File.exist?(file_path)
      puts "Error: specimens.json file not found at #{file_path}"
      exit 1
    end

    puts 'Checking specimens.json file structure...'

    # Read the first few records to check structure
    begin
      file = File.open(file_path)
      # Read first 1000 characters
      sample = file.read(1000)
      file.close

      # Try to parse the sample
      if sample.start_with?('[')
        puts 'File appears to be a JSON array'

        # Parse the full file to validate
        specimens_data = JSON.parse(File.read(file_path))

        if specimens_data.is_a?(Array) && !specimens_data.empty?
          sample_record = specimens_data.first
          puts 'Sample record structure:'
          puts JSON.pretty_generate(sample_record)

          # Check if the structure matches what we expect
          if sample_record.key?('id') && sample_record.key?('tracking_number')
            puts 'File structure is valid for import'
            puts "Total records in file: #{specimens_data.size}"
          else
            puts "Warning: Expected keys 'id' and 'tracking_number' not found in sample record"
          end
        else
          puts 'Warning: File does not contain a non-empty array'
        end
      else
        puts "Warning: File does not start with '[', may not be a JSON array"
      end
    rescue JSON::ParserError => e
      puts "Error parsing JSON file: #{e.message}"
    rescue StandardError => e
      puts "Error checking file: #{e.message}"
    end
  end
end
